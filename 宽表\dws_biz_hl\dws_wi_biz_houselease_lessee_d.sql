with tzccz_yxczfxx as (
    select 
     DISTINCT
    ,id as intnt_rent_id -- 意向承租方ID
    ,c1.reg_intend_lessee_cnt 
    ,c2.mm_intend_lessee_cnt
    ,c3.bid_intend_lessee_cnt
    ,a.intnt_rent_nm AS intnt_rent_nm -- 意向承租方名称
    ,intnt_rent_tp_dsc AS intnt_rent_tp_dsc -- 意向承租方类型描述
    ,prov AS prov -- 省
    ,region_county AS region_county -- 区(县)
    ,second_place_bidder as lessee_name_second_bid --竞价排名第二的意向承租方名称	
    ,second_place_amount as lessee_offer_second_bid --竞价排名第二的意向承租方报价（元/平方米/天）	
    ,third_place_bidder as lessee_name_third_bid --竞价排名第三的意向承租方名称	
    ,third_place_amount as lessee_offer_third_bid --竞价排名第三的意向承租方报价（元/平方米/天）
from std.std_bjhl_tzccz_yxczfxx_d a
left join (
    select project_id,count(distinct intnt_rent_nm) as reg_intend_lessee_cnt 
from std.std_bjhl_tzccz_yxczfxx_d where dt = '${dmp_day}' 
) c1
on a.project_id = c1.project_id
left join (
    select project_id,count(distinct intnt_rent_nm) as mm_intend_lessee_cnt 
    from std.std_bjhl_tzccz_yxczfxx_d where dt = '${dmp_day}' 
    and deposit_sts_dsc = '已交纳' 
) c2
on a.project_id = c2.project_id
left join (
    select project_id,count(distinct intnt_rent_nm) as bid_intend_lessee_cnt 
    from std.std_bjhl_tzccz_yxczfxx_d where dt = '${dmp_day}' 
    and final_qlfy_rst_dsc = '获得资格'
) c3
on a.project_id = c3.project_id
left join (
    SELECT 
    project_id,
    MAX(CASE WHEN bid_rank = 2 THEN intnt_rent_nm END) AS second_place_bidder, -- 竞价排名第二的意向承租方名称
    MAX(CASE WHEN bid_rank = 2 THEN bid_amount END) AS second_place_amount, -- 竞价排名第二的报价金额
    MAX(CASE WHEN bid_rank = 3 THEN intnt_rent_nm END) AS third_place_bidder, -- 竞价排名第三的意向承租方名称
    MAX(CASE WHEN bid_rank = 3 THEN bid_amount END) AS third_place_amount -- 竞价排名第三的报价金额
FROM (
        SELECT 
            project_id,
            intnt_rent_nm AS intnt_rent_nm, -- 意向承租方名称
            BJJE AS bid_amount, -- 竞价金额
            ROW_NUMBER() OVER (
                PARTITION BY project_id 
                ORDER BY BJJE DESC
            ) AS bid_rank -- 按竞价金额从高到低排名
        FROM std.std_bjhl_tzccz_yxczfxx_d
        WHERE dt = '${dmp_day}'
            AND project_id IS NOT NULL
            AND BJJE IS NOT NULL -- 确保有竞价金额
            AND intnt_rent_nm IS NOT NULL -- 确保有意向承租方名称
    ) ranked_bidders
    WHERE bid_rank IN (1,2, 3) -- 只关注第二和第三名
    and dt = '${dmp_day}' 
    GROUP BY project_id
) b
on a.project_id = b.project_id
where dt = '${dmp_day}' 
and project_id is not null

)

select
 * from 
tzccz_yxczfxx a
left join 
(
select 
project_id, -- 项目ID
intnt_rent_id, -- 意向承租方ID
intnt_rent_nm, -- 意向承租方名称
province_house, -- 【房屋】省份
city_house, -- 【房屋】城市
district_house, -- 【房屋】市辖区
biz_district_house, -- 【房屋】商圈
dt_address_house, -- 【房屋】详细地址
'' as deal_address, -- 项目的房屋所在楼宇
deal_date, -- 成交日期
deal_price_std_sq_d, -- 成交租金价格_平方米/天
deal_total_price, -- 成交总价（元）
asset_cate, -- 资产类型
list_price_std_sq_d, -- 挂牌价格_平方米/天
list_total_price_cal, -- 挂牌总价_计算值(元)
list_price_sep, --挂牌总价_录入值(元)
lease_area, -- 出租面积
lease_prd_std_d, -- 挂牌租赁期_标准化（天）
lessee_name_second_bid, -- 竞价排名第二的意向承租方名称
lessee_name_third_bid, -- 竞价排名第三的意向承租方名称
case when deal_date is not null or intnt_rent_nm in (lessee_name_second_bid,lessee_name_third_bid) then 1 else 0 end as is_top3_bidder -- 是否在前三名
from dws_wi_biz_houselease_lessee_d b
)


WITH LesseeBase AS (
    -- 基础数据：筛选出意向承租方相关的所有记录
    SELECT
        project_id,
        intnt_rent_id,
        intnt_rent_nm,
        province_house,
        city_house,
        district_house,
        biz_district_house,
        dt_address_house,
        deal_date,
        deal_price_std_sq_d,
        deal_total_price,
        asset_cate,
        list_price_std_sq_d,
        list_total_price_cal,
        list_price_sep,
        lease_area,
        lease_prd_std_d,
        lessee_name_second_bid,
        lessee_name_third_bid,
        -- 用于判断是否成交或在Top3
        CASE WHEN deal_date IS NOT NULL THEN 1 ELSE 0 END AS is_deal,
        CASE WHEN intnt_rent_nm IN (lessee_name_second_bid, lessee_name_third_bid) THEN 1 ELSE 0 END AS is_top3_bidder_only_rank -- 注意这里只判断竞价排名，不考虑成交
    FROM
        dws_wi_biz_houselease_lessee_d
),
-- 计算历史成交相关指标
LesseeDealHistory AS (
    SELECT
        intnt_rent_id,
        intnt_rent_nm AS intend_lessee_name, -- 意向承租方名称
        -- 假设意向承租方类型、省份、城市在当前查询中无法直接从承租方维度获得，可能需要从最近一次成交或报价中推断，或保持为空
        -- '未知类型' AS intend_lessee_type, -- 意向承租方类型 (如果无法推断，可保留为空或默认值)
        -- MAX(province_house) AS lessee_province, -- 所在省份 (简单取最大，实际可能需要更复杂的逻辑)
        -- MAX(city_house) AS lessee_city, -- 所在城市 (简单取最大，实际可能需要更复杂的逻辑)

        -- 是否成交过房屋出租项目
        MAX(CASE WHEN is_deal = 1 THEN 1 ELSE 0 END) AS is_his_house_prj,
        -- 历史成交项目数量
        COUNT(DISTINCT CASE WHEN is_deal = 1 THEN project_id END) AS his_deal_prj_cnt,
        -- 历史平均成交租金价格
        AVG(CASE WHEN is_deal = 1 THEN deal_price_std_sq_d END) AS his_deal_price_avg,
        -- 历史成交租金总额
        SUM(CASE WHEN is_deal = 1 THEN deal_total_price END) AS his_deal_price_total,
        -- 历史成交的房屋类项目数量
        COUNT(DISTINCT CASE WHEN is_deal = 1 AND asset_cate = '房屋' THEN project_id END) AS his_deal_house_prj_cnt,
        -- 历史成交的土地类项目数量
        COUNT(DISTINCT CASE WHEN is_deal = 1 AND asset_cate = '土地' THEN project_id END) AS his_deal_land_prj_cnt,
        -- 历史单项目成交租金价格_最大值
        MAX(CASE WHEN is_deal = 1 THEN deal_price_std_sq_d END) AS his_deal_price_max,
        -- 历史单项目成交租金总额_最大值
        MAX(CASE WHEN is_deal = 1 THEN deal_total_price END) AS his_deal_total_price_max,

        -- 历史成交项目最多的省-市-区
        SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN is_deal = 1 THEN CONCAT(province_house, '-', city_house, '-', district_house) END ORDER BY deal_date DESC), ',', 1) AS his_deal_district_most,
        -- 历史成交项目最多的商圈
        SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN is_deal = 1 THEN biz_district_house END ORDER BY deal_date DESC), ',', 1) AS his_deal_bizscop_most,
        -- 历史成交项目最多的详细地址
        SUBSTRING_INDEX(GROUP_CONCAT(CASE WHEN is_deal = 1 THEN dt_address_house END ORDER BY deal_date DESC), ',', 1) AS his_deal_address_most
    FROM
        LesseeBase
    GROUP BY
        intnt_rent_id, intnt_rent_nm
),
-- 计算最近一次成交相关指标
LesseeLastDeal AS (
    SELECT
        intnt_rent_id,
        project_id AS last_deal_prj_nm, -- 最近一次成交项目名称 (这里用project_id作为项目名称，如果实际有nm字段请替换)
        deal_date AS last_deal_date, -- 最近一次成交日期
        DATEDIFF(CURRENT_DATE(), deal_date) AS last_deal_since_days, -- 最近一次成交距今天数
        asset_cate AS last_deal_asset_cate, -- 最近一次成交项目的资产类型
        deal_price_std_sq_d AS last_deal_price, -- 最近一次成交租金价格
        lease_area AS last_deal_area, -- 最近一次成交的出租面积
        lease_prd_std_d AS last_deal_period, -- 最近一次成交的租赁期
        dt_address_house AS last_deal_location, -- 最近一次成交项目的房屋坐落位置
        CONCAT(province_house, '-', city_house, '-', district_house) AS last_deal_district, -- 最近一次成交项目的房屋所在省-市-区
        biz_district_house AS last_deal_bizscop, -- 最近一次成交项目的房屋所在商圈
        dt_address_house AS last_deal_address, -- 最近一次成交项目的房屋所在楼宇 (这里同坐落位置，如需区分请提供不同字段)
        ROW_NUMBER() OVER(PARTITION BY intnt_rent_id ORDER BY deal_date DESC) AS rn -- 按成交日期倒序排序，取最新
    FROM
        LesseeBase
    WHERE
        is_deal = 1
),
-- 计算历史报价相关指标
LesseeBidHistory AS (
    SELECT
        intnt_rent_id,
        COUNT(DISTINCT project_id) AS his_bid_prj_cnt, -- 历史报价过的项目数量
        COUNT(DISTINCT CASE WHEN asset_cate = '房屋' THEN project_id END) AS his_bid_house_cate_cnt, -- 历史报价过的房屋类项目数量
        COUNT(DISTINCT CASE WHEN asset_cate = '土地' THEN project_id END) AS his_bid_land_cate_cnt, -- 历史报价过的土地类项目数量

        -- 历史报价最多的房屋所在省-市-区
        SUBSTRING_INDEX(GROUP_CONCAT(CONCAT(province_house, '-', city_house, '-', district_house) ORDER BY deal_date DESC), ',', 1) AS his_bid_district_most,
        -- 历史报价最多的房屋所在商圈
        SUBSTRING_INDEX(GROUP_CONCAT(biz_district_house ORDER BY deal_date DESC), ',', 1) AS his_bid_bizscop_most,

        SUM(CASE WHEN is_deal = 1 THEN 1 ELSE 0 END) AS deal_count, -- 成交次数
        COUNT(project_id) AS bid_count, -- 报价总次数 (这里是针对每个项目意向承租方都有记录)
        SUM(CASE WHEN is_deal = 1 OR is_top3_bidder_only_rank = 1 THEN 1 ELSE 0 END) AS top3_bid_count -- 历史报价在项目总报价中排名前三的次数 (成交或竞价排名在前三)
    FROM
        LesseeBase
    GROUP BY
        intnt_rent_id
),
-- 计算最近一次报价相关指标
LesseeLastBid AS (
    SELECT
        intnt_rent_id,
        deal_date AS last_bid_date, -- 最近一次报价的日期 (假设deal_date也代表了最近一次报价日期，如果报价日期是独立字段请替换)
        project_id AS last_bid_prj_nm, -- 最近一次报价的项目名称
        project_id AS last_bid_prj_id, -- 最近一次报价的项目编号
        asset_cate AS last_bid_asset_cate, -- 最近一次报价的资产类型
        list_price_std_sq_d AS last_bid_prj_price, -- 最近一次报价的租金价格 (假设是挂牌价)
        list_total_price_cal AS last_bid_prj_total_price, -- 最近一次报价的租金总额 (假设是挂牌总价)
        CONCAT(province_house, '-', city_house, '-', district_house) AS last_bid_district, -- 最近一次报价的地区_省-市-区
        biz_district_house AS last_bid_bizscop, -- 最近一次报价的地区_商圈
        ROW_NUMBER() OVER(PARTITION BY intnt_rent_id ORDER BY deal_date DESC) AS rn -- 按日期倒序排序，取最新
    FROM
        LesseeBase
),
-- 汇总所有承租方信息
AggregatedLesseeInfo AS (
    SELECT
        lb.intnt_rent_id,
        lb.intnt_rent_nm AS intend_lessee_name, -- 意向承租方名称 (从基础数据中取，确保与历史数据一致)
        -- 假设类型和城市信息需要从最近一次成交或报价中推断，这里做一个简单示例，如果数据源有明确字段，请直接关联
        (SELECT asset_cate FROM LesseeLastDeal WHERE intnt_rent_id = lb.intnt_rent_id AND rn = 1 LIMIT 1) AS intend_lessee_type, -- 意向承租方类型 (这里简单使用最近一次成交的资产类型，可能不准确)
        (SELECT province_house FROM LesseeBase WHERE intnt_rent_id = lb.intnt_rent_id ORDER BY deal_date DESC LIMIT 1) AS lessee_province, -- 所在省份 (取最近一次记录的省份)
        (SELECT city_house FROM LesseeBase WHERE intnt_rent_id = lb.intnt_rent_id ORDER BY deal_date DESC LIMIT 1) AS lessee_city, -- 所在城市 (取最近一次记录的城市)

        ldh.is_his_house_prj,
        ldh.his_deal_prj_cnt,
        ldh.his_deal_price_avg,
        ldh.his_deal_price_total,
        ldh.his_deal_house_prj_cnt,
        ldh.his_deal_land_prj_cnt,
        ldh.his_deal_price_max,
        ldh.his_deal_total_price_max,
        ldh.his_deal_district_most,
        ldh.his_deal_bizscop_most,
        ldh.his_deal_address_most,

        lld.last_deal_prj_nm,
        lld.last_deal_date,
        lld.last_deal_since_days,
        lld.last_deal_asset_cate,
        lld.last_deal_price,
        lld.last_deal_area,
        lld.last_deal_period,
        lld.last_deal_location,
        lld.last_deal_district,
        lld.last_deal_bizscop,
        lld.last_deal_address,

        lbh.his_bid_prj_cnt,
        lbh.his_bid_house_cate_cnt,
        lbh.his_bid_land_cate_cnt,
        lbh.his_bid_district_most,
        lbh.his_bid_bizscop_most,

        llb.last_bid_date,
        llb.last_bid_prj_nm,
        llb.last_bid_prj_id,
        llb.last_bid_asset_cate,
        llb.last_bid_prj_price,
        llb.last_bid_prj_total_price,
        llb.last_bid_district,
        llb.last_bid_bizscop,

        -- 历史报价成交率 (如果报价总次数为0，则为0)
        COALESCE(lbh.deal_count / NULLIF(lbh.bid_count, 0), 0) AS his_bid_deal_rate,
        lbh.top3_bid_count AS his_bid_top3_num, -- 历史报价在项目总报价中排名前三的次数
        -- 历史报价在项目总报价中排名前三的次数占比 (如果报价总次数为0，则为0)
        COALESCE(lbh.top3_bid_count / NULLIF(lbh.bid_count, 0), 0) AS his_bid_top3_rate
    FROM
        (SELECT DISTINCT intnt_rent_id, intnt_rent_nm FROM LesseeBase) AS lb -- 获取所有不重复的意向承租方ID和名称
    LEFT JOIN
        LesseeDealHistory AS ldh ON lb.intnt_rent_id = ldh.intnt_rent_id
    LEFT JOIN
        LesseeLastDeal AS lld ON lb.intnt_rent_id = lld.intnt_rent_id AND lld.rn = 1
    LEFT JOIN
        LesseeBidHistory AS lbh ON lb.intnt_rent_id = lbh.intnt_rent_id
    LEFT JOIN
        LesseeLastBid AS llb ON lb.intnt_rent_id = llb.intnt_rent_id AND llb.rn = 1
)
-- 最终查询：将原始项目信息与聚合后的承租方信息联接
SELECT
    t1.project_id, -- 项目ID
    t1.intnt_rent_id, -- 意向承租方ID
    t1.intnt_rent_nm, -- 意向承租方名称
    t1.province_house, -- 【房屋】省份
    t1.city_house, -- 【房屋】城市
    t1.district_house, -- 【房屋】市辖区
    t1.biz_district_house, -- 【房屋】商圈
    t1.dt_address_house, -- 【房屋】详细地址
    '' AS deal_address, -- 项目的房屋所在楼宇 (原始字段中未找到对应，保持空)
    t1.deal_date, -- 成交日期
    t1.deal_price_std_sq_d, -- 成交租金价格_平方米/天
    t1.deal_total_price, -- 成交总价（元）
    t1.asset_cate, -- 资产类型
    t1.list_price_std_sq_d, -- 挂牌价格_平方米/天
    t1.list_total_price_cal, -- 挂牌总价_计算值(元)
    t1.list_price_sep, -- 挂牌总价_录入值(元)
    t1.lease_area, -- 出租面积
    t1.lease_prd_std_d, -- 挂牌租赁期_标准化（天）
    t1.lessee_name_second_bid, -- 竞价排名第二的意向承租方名称
    t1.lessee_name_third_bid, -- 竞价排名第三的意向承租方名称
    CASE WHEN t1.deal_date IS NOT NULL OR t1.intnt_rent_nm IN (t1.lessee_name_second_bid, t1.lessee_name_third_bid) THEN 1 ELSE 0 END AS is_top3_bidder, -- 是否在前三名
    t2.intend_lessee_name, -- 意向承租方名称 (从聚合信息中取)
    t2.intend_lessee_type, -- 意向承租方类型
    t2.lessee_province, -- 所在省份
    t2.lessee_city, -- 所在城市
    t2.is_his_house_prj, -- 是否成交过房屋出租项目
    t2.his_deal_prj_cnt, -- 历史成交项目数量
    t2.his_deal_price_avg, -- 历史平均成交租金价格
    t2.his_deal_price_total, -- 历史成交租金总额
    t2.his_deal_house_prj_cnt, -- 历史成交的房屋类项目数量
    t2.his_deal_land_prj_cnt, -- 历史成交的土地类项目数量
    t2.his_deal_price_max, -- 历史单项目成交租金价格_最大值
    t2.his_deal_total_price_max, -- 历史单项目成交租金总额_最大值
    t2.his_deal_district_most, -- 历史成交项目最多的省-市-区
    t2.his_deal_bizscop_most, -- 历史成交项目最多的商圈
    t2.his_deal_address_most, -- 历史成交项目最多的详细地址
    t2.last_deal_prj_nm, -- 最近一次成交项目名称
    t2.last_deal_date, -- 最近一次成交日期
    t2.last_deal_since_days, -- 最近一次成交距今天数
    t2.last_deal_asset_cate, -- 最近一次成交项目的资产类型
    t2.last_deal_price, -- 最近一次成交租金价格
    t2.last_deal_area, -- 最近一次成交的出租面积
    t2.last_deal_period, -- 最近一次成交的租赁期
    t2.last_deal_location, -- 最近一次成交项目的房屋坐落位置
    t2.last_deal_district, -- 最近一次成交项目的房屋所在省-市-区
    t2.last_deal_bizscop, -- 最近一次成交项目的房屋所在商圈
    t2.last_deal_address, -- 最近一次成交项目的房屋所在楼宇
    t2.his_bid_prj_cnt, -- 历史报价过的项目数量
    t2.his_bid_house_cate_cnt, -- 历史报价过的房屋类项目数量
    t2.his_bid_land_cate_cnt, -- 历史报价过的土地类项目数量
    t2.his_bid_district_most, -- 历史报价最多的房屋所在省-市-区
    t2.his_bid_bizscop_most, -- 历史报价最多的房屋所在商圈
    t2.last_bid_date, -- 最近一次报价的日期
    t2.last_bid_prj_nm, -- 最近一次报价的项目名称
    t2.last_bid_prj_id, -- 最近一次报价的项目编号
    t2.last_bid_asset_cate, -- 最近一次报价的资产类型
    t2.last_bid_prj_price, -- 最近一次报价的租金价格
    t2.last_bid_prj_total_price, -- 最近一次报价的租金总额
    t2.last_bid_district, -- 最近一次报价的地区_省-市-区
    t2.last_bid_bizscop, -- 最近一次报价的地区_商圈
    t2.his_bid_deal_rate, -- 历史报价成交率
    t2.his_bid_top3_num, -- 历史报价在项目总报价中排名前三的次数
    t2.his_bid_top3_rate -- 历史报价在项目总报价中排名前三的次数占比
FROM
    dws_wi_biz_houselease_lessee_d AS t1
LEFT JOIN
    AggregatedLesseeInfo AS t2 ON t1.intnt_rent_id = t2.intnt_rent_id;

SELECT
    project_id,
    '**' AS his_deal_prj_cnt,                -- 历史成交项目数量
    '**' AS his_deal_price_avg,              -- 历史平均成交租金价格
    '**' AS his_deal_price_total,            -- 历史成交租金总额
    '**' AS his_deal_house_prj_cnt,          -- 历史成交的房屋类项目数量
    '**' AS his_deal_land_prj_cnt,           -- 历史成交的土地类项目数量
    '**' AS his_deal_price_max,              -- 历史单项目成交租金价格_最大值
    '**' AS his_deal_total_price_max,        -- 历史单项目成交租金总额_最大值
    '**' AS his_deal_district_most,          -- 历史成交项目最多的省-市-区
    '**' AS his_deal_bizscop_most,           -- 历史成交项目最多的商圈
    '**' AS his_deal_address_most,           -- 历史成交项目最多的详细地址
    '**' AS last_deal_prj_nm,                -- 最近一次成交项目名称
    '**' AS last_deal_date,                  -- 最近一次成交日期
    '**' AS last_deal_since_days,            -- 最近一次成交距今天数
    '**' AS last_deal_asset_cate,            -- 最近一次成交项目的资产类型
    '**' AS last_deal_price,                 -- 最近一次成交租金价格
    '**' AS last_deal_area,                  -- 最近一次成交的出租面积
    '**' AS last_deal_period,                -- 最近一次成交的租赁期
    '**' AS last_deal_location,              -- 最近一次成交项目的房屋坐落位置
    '**' AS last_deal_district,              -- 最近一次成交项目的房屋所在省-市-区
    '**' AS last_deal_bizscop,               -- 最近一次成交项目的房屋所在商圈
    '**' AS last_deal_address,               -- 最近一次成交项目的房屋所在楼宇
    '**' AS his_bid_prj_cnt,                 -- 历史报价过的项目数量
    '**' AS his_bid_house_cate_cnt,          -- 历史报价过的房屋类项目数量
    '**' AS his_bid_land_cate_cnt,           -- 历史报价过的土地类项目数量
    '**' AS his_bid_district_most,           -- 历史报价最多的房屋所在省-市-区
    '**' AS his_bid_bizscop_most,            -- 历史报价最多的房屋所在商圈
    '**' AS last_bid_date,                   -- 最近一次报价的日期
    '**' AS last_bid_prj_nm,                 -- 最近一次报价的项目名称
    '**' AS last_bid_prj_id,                 -- 最近一次报价的项目编号
    '**' AS last_bid_asset_cate,             -- 最近一次报价的资产类型
    '**' AS last_bid_prj_price,              -- 最近一次报价的租金价格
    '**' AS last_bid_prj_total_price,        -- 最近一次报价的租金总额
    '**' AS last_bid_district,               -- 最近一次报价的地区_省-市-区
    '**' AS last_bid_bizscop,                -- 最近一次报价的地区_商圈
    '**' AS his_bid_deal_rate,               -- 历史报价成交率
    first_place_bidder AS his_bid_top3_num,  -- 历史报价在项目总报价中排名前三的次数
    '**' AS his_bid_top3_rate                -- 历史报价在项目总报价中排名前三的次数占比
FROM tzccz_yxczfxx a
dws_wi_biz_houselease_lessee_d b
on a.intnt_rent_id = intnt_rent_id



SELECT
    a.intnt_rent_nm AS intend_lessee_name,        -- 意向承租方名称
    a.intnt_rent_tp_dsc AS intend_lessee_type,        -- 意向承租方类型
    a.prov AS lessee_province,           -- 所在省份
    a.region_county AS lessee_city,               -- 所在城市
    '**' AS is_his_house_prj,          -- 是否成交过房屋出租项目
    '**' AS his_deal_prj_cnt,          -- 历史成交项目数量
    '**' AS his_deal_price_avg,        -- 历史平均成交租金价格
    '**' AS his_deal_price_total,      -- 历史成交租金总额
    '**' AS his_deal_house_prj_cnt,    -- 历史成交的房屋类项目数量
    '**' AS his_deal_land_prj_cnt,     -- 历史成交的土地类项目数量
    '**' AS his_deal_price_max,        -- 历史单项目成交租金价格_最大值
    '**' AS his_deal_total_price_max,  -- 历史单项目成交租金总额_最大值
    '**' AS his_deal_district_most,    -- 历史成交项目最多的省-市-区
    '**' AS his_deal_bizscop_most,     -- 历史成交项目最多的商圈
    '**' AS his_deal_address_most,     -- 历史成交项目最多的详细地址
    '**' AS last_deal_prj_nm,          -- 最近一次成交项目名称
    '**' AS last_deal_date,            -- 最近一次成交日期
    '**' AS last_deal_since_days,      -- 最近一次成交距今天数
    '**' AS last_deal_asset_cate,      -- 最近一次成交项目的资产类型
    '**' AS last_deal_price,           -- 最近一次成交租金价格
    '**' AS last_deal_area,            -- 最近一次成交的出租面积
    '**' AS last_deal_period,          -- 最近一次成交的租赁期
    '**' AS last_deal_location,        -- 最近一次成交项目的房屋坐落位置
    '**' AS last_deal_district,        -- 最近一次成交项目的房屋所在省-市-区
    '**' AS last_deal_bizscop_most,    -- 最近一次成交项目的房屋所在商圈
    '**' AS last_deal_address,         -- 最近一次成交项目的房屋所在楼宇
    '**' AS his_bid_prj_cnt,           -- 历史报价过的项目数量
    '**' AS his_bid_house_cate_cnt,    -- 历史报价过的房屋类项目数量
    '**' AS his_bid_land_cate_cnt,     -- 历史报价过的土地类项目数量
    '**' AS his_bid_district_most,     -- 历史报价最多的房屋所在省-市-区
    '**' AS his_bid_bizscop_most,      -- 历史报价最多的房屋所在商圈
    '**' AS last_bid_date,             -- 最近一次报价的日期
    '**' AS last_bid_prj_nm,           -- 最近一次报价的项目名称
    '**' AS last_bid_prj_id,           -- 最近一次报价的项目编号
    '**' AS last_bid_asset_cate,       -- 最近一次报价的资产类型
    '**' AS last_bid_prj_price,        -- 最近一次报价的租金价格
    '**' AS last_bid_prj_total_price,  -- 最近一次报价的租金总额
    '**' AS last_bid_district,         -- 最近一次报价的地区_省-市-区
    '**' AS last_bid_bizscop,          -- 最近一次报价的地区_商圈
    '**' AS his_bid_deal_rate,         -- 历史报价成交率
    '**' AS his_bid_top3_num,          -- 历史报价在项目总报价中排名前三的次数
    '**' AS his_bid_top3_rate,         -- 历史报价在项目总报价中排名前三的次数占比
    '**' AS last_year_view_prj_cnt,    -- 最近一年浏览的项目数量
    '**' AS last_year_view_district_most, -- 最近一年浏览次数最多的省-市-区
    '**' AS last_year_view_bizscop_most,  -- 最近一年浏览次数最多的商圈
    '**' AS last_year_view_price_std_avg, -- 最近一年浏览项目的平均租金价格
    '**' AS last_year_view_total_price_avg, -- 最近一年浏览项目的平均租金总额
    '**' AS last_year_follow_prj_cnt,      -- 最近一年关注的项目数量
    '**' AS last_year_follow_district_most,-- 最近一年关注次数最多的省-市-区
    '**' AS last_year_follow_bizscop_most, -- 最近一年关注次数最多的商圈
    '**' AS last_year_follow_price_std_avg,-- 最近一年关注项目的平均租金价格
    '**' AS last_year_follow_total_price_avg,-- 最近一年关注项目的平均租金总额
    '**' AS last_year_share_prj_cnt,       -- 最近一年分享的项目数量
    '**' AS last_year_share_district_most, -- 最近一年分享次数最多的省-市-区
    '**' AS last_year_share_bizscop_most,  -- 最近一年分享次数最多的商圈
    '**' AS last_year_share_price_std_avg, -- 最近一年分享项目的平均租金价格
    '**' AS last_year_share_total_price_avg,-- 最近一年分享项目的平均租金总额
    '**' AS last_active_date              -- 最近一次活跃日期
FROM tzccz_yxczfxx a
left join 



