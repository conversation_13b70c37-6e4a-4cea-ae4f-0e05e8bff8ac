select
date_format(current_timestamp(), 'yyyy-MM-dd') as effect_time, -- 数据生效日期
mber_no	as agent_no,	-- 会员编号
mber_nm as compy_name, --公司名称
cust_no as cust_no, --客户号
'' as updateby, -- 更新人
current_timestamp() as update_time, -- 更新时间
prof_srv_mem_type_dsc,
mber_cgy_cd_dsc
from (
    select *,
           row_number() over (partition by edw_non_pk_md5 order by edw_star_dt desc) as rn
    from dim.dim_pty_mber_cust_info
    where dt = '${dmp_day}'
    and edw_end_dt = '20991231'
    and (
        mber_cgy_cd_dsc in ('交易服务会员','国企会员') or prof_srv_mem_type_dsc in ('拍卖机构','专项服务机构')
    )
    and mber_stat_cd_dsc = '启用'
) t
where rn = 1