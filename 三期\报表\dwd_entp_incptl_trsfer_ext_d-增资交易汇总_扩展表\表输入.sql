SELECT 
    A.project_code                   AS project_code -- 项目编号
   ,B.nm                             AS buyer_name     -- 投资方名称
   ,'否'                              AS is_joint_member -- 是否联合体成员
   ,CASE 
        WHEN A.project_code LIKE 'G6%' THEN '公开'
        WHEN A.project_code LIKE 'G7%' OR A.project_code LIKE 'G8%' THEN '非公开' 
    END                              AS transparency -- 公开/非公开 
   ,CASE 
            WHEN A.intnd_rs_cptl_crpnd_tp = 1 THEN 
                CASE 
                    WHEN A.intnd_rs_cptl_crpnd_mod = 2 THEN 
                        CASE WHEN A.intnd_rs_cptl_crpnd_hold_shr_pct = 0 THEN '0%'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_hold_shr_pct || '%', '(\\.)?0+%$', '%')
                        END
                    WHEN A.intnd_rs_cptl_crpnd_mod = 1 THEN 
                        CASE WHEN A.intnd_rs_cptl_crpnd_hold_shr_pct_min = 0 THEN '0%'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_hold_shr_pct_min || '%', '(\\.)?0+%$', '%')
                        END || '至' || 
                        CASE WHEN A.intnd_rs_cptl_crpnd_hold_shr_pct_max = 0 THEN '0%'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_hold_shr_pct_max || '%', '(\\.)?0+%$', '%')
                        END
                END
            WHEN A.intnd_rs_cptl_crpnd_tp = 2 THEN 
                CASE 
                    WHEN A.intnd_rs_cptl_crpnd_mod = 2 THEN 
                        CASE WHEN A.intnd_rs_cptl_crpnd_shr_num_w_share = 0 THEN '0万股'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_shr_num_w_share || '万股', '(\\.)?0+万股$', '万股')
                        END
                    WHEN A.intnd_rs_cptl_crpnd_mod = 1 THEN 
                        CASE WHEN A.intnd_rs_cptl_crpnd_shr_num_min_w_share = 0 THEN '0万股'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_shr_num_min_w_share || '万股', '(\\.)?0+万股$', '万股')
                        END || '至' || 
                        CASE WHEN A.intnd_rs_cptl_crpnd_shr_num_max_w_share = 0 THEN '0万股'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_shr_num_max_w_share || '万股', '(\\.)?0+万股$', '万股')
                        END
                END 
    END				                 AS listing_cap_inc_ratio -- 挂牌增资比例/股数 
   ,G.zzqgqjg                        AS pre_cap_inc_shareholders -- 增资前的股东 
   ,REGEXP_REPLACE(A.zzqgyzbczzbl || '%', '\.?0+%$', '%')      AS pre_municipal_state_owned_ratio -- 增资前全部国有股东持股比例（仅含市国资委监管国有企业股东） 
   ,REGEXP_REPLACE(A.zzqgyzbczzbl || '%', '\.?0+%$', '%')      AS pre_all_state_owned_ratio -- 增资前全部国有股东持股比例（含其他国资监管机构下属企业股东） 
   ,A.prefer_mod_dsc                 AS deal_method -- 成交方式 
   ,substr(A.evaluate_date,0,10)     AS assessment_baseline_date -- 评估基准日 
   ,A.asset_sum_book_val             AS total_assets -- 资产总额（万元） 
   ,A.liab_sum_book_val              AS total_liabilities -- 负债总额（万元） 
   ,A.zzhgyzbczzbl                   AS post_municipal_state_owned_ratio -- 项目完成后全部国有股东持股比例（%）（仅含市国资委监管国有企业股东） 
   ,A.zzhgyzbczzbl                   AS post_all_state_owned_ratio -- 项目完成后全部国有股东持股比例（%）（含其他国资监管机构下属企业股东） 
   ,A.icap_af_fincer_economy_type_dsc  AS post_project_nature -- 项目完成后标的企业性质 
   ,CASE 
        WHEN A.mdl_chk_opin_tp_dsc = '股权激励' THEN A.mdl_chk_opin_tp_dsc 
        ELSE '' 
    END                              AS esop -- 股权激励/员工持股 
   ,CASE 
        WHEN A.mdl_chk_opin_tp_dsc IN ('特定投资方增资','所出资企业直接或指定其控股、实际控制的其他子企业参与增资') THEN A.mdl_chk_opin_tp_dsc 
        ELSE '' 
    END                              AS reorg -- 集团内部重组/股权结构调整/所出资企业直接或指定其控股、实际控制的其他子企业参与增资/债转股 
   ,CASE 
        WHEN A.mdl_chk_opin_tp_dsc IN ('国有资本经营预算拨款','国有资本经营有预算拨款') THEN A.mdl_chk_opin_tp_dsc   
        ELSE '' 
    END                              AS budget -- 国有资本经营预算拨款 
   ,CASE 
        WHEN A.mdl_chk_opin_tp = 3   THEN '所有者权益类转增' 
        ELSE '' 
    END                              AS equity_to_capital -- 所有者权益转增资本 
   ,CASE 
        WHEN A.mdl_chk_opin_tp_dsc IN ('原股东同比例','原股东不同比例') THEN A.mdl_chk_opin_tp_dsc 
        ELSE '' 
    END                              AS original_shareholder_increase -- 原股东增资（原股东同比例增资/原股东不同比例增资） 
   ,substr(A.approval_date,0,10)     AS approval_date -- 批准日期 
   ,C.qualified_investors_num        AS qualified_investors_num -- 征集合格意向投资人个数 
   ,CASE WHEN H.single_investor_amt IS NOT NULL THEN H.single_investor_amt ELSE 
        CASE 
                WHEN B.is_unite_ivs = 1 THEN D.repre_invest_total_w_yuan  
                ELSE D.new_fndd_cptl_w_yuan 
            END  
    END                             AS single_investor_cap_inc_amt -- 单个投资人增资金额（万元） 
   ,CASE 
        WHEN B.is_unite_ivs = 1 THEN D.repre_new_fndd_cptl_w_yuan  
        ELSE D.invest_total_w_yuan 
    END                              AS single_investor_reg_cap_amt -- 单个投资人增加注册资本金额（万元） 
   ,B1.nm                            AS investor_industry -- 投资人所属行业 
   ,E.invest_total_w_yuan            AS total_new_capital -- 新增出资资本合计数 
   ,CASE WHEN H.financing_amt IS NOT NULL THEN H.financing_amt ELSE E.new_fndd_cptl_w_yuan   END    AS total_investment_funds -- 投资资金总额合计数 
   ,F.max_date                       AS audit_year -- 审计年度 
   ,H.mix_own_type                   AS mix_own_type -- 混改类型（补录） 
   ,H.land_use_amt                   AS land_use_amt -- 涉及土地使用权金额（补录） 
   ,H.tech_asset_amt                 AS tech_asset_amt -- 涉及技术资产金额（补录） 
   ,NULL                             AS bond_amt -- 涉及债权转让金额
   ,A.custd_org_depdc_prov           AS custd_org_depdc_prov   -- 监管机构属地(省)
   ,CASE
         WHEN A.intnd_rs_cptl_tot_amt_cmnt IS NOT NULL THEN
             substr(A.intnd_rs_cptl_tot_amt_cmnt, 3000, 1)
         ELSE
             CASE A.intnd_new_cptl_cptl_mod
                 WHEN 2 THEN
                     REGEXP_REPLACE(A.intnd_rs_cptl_tot_amt_w_yuan || '', '\.?0+$', '')
                 WHEN 1 THEN
                     REGEXP_REPLACE(A.intnd_rs_cptl_tot_amt_min_w_yuan || '', '\.?0+$', '') || '至' || 
                     REGEXP_REPLACE(A.intnd_rs_cptl_tot_amt_max_w_yuan || '', '\.?0+$', '')
             END
     END AS target_funds_amt               -- 拟募集资金金额（万元）
    ,A.equity_book_val_w_yuan AS equity -- 所有者权益（万元） 
    ,J.zzhgqjg AS post_project_shareholders      -- 项目完成后的股东 
    ,J.zzhgqjg AS post_project_shareholders_ratio -- 项目完成后的股东持股比例
    ,H.investor_type AS investor_type -- 投资人类型
    ,A.economy_type_dsc AS  economy_type_dsc -- 增资前标的企业性质
    ,H.pre_shr_ratio    AS  pre_shr_ratio -- 增资前的股东持股比例
    ,H.biz_income AS biz_income -- 营业收入
    ,H.biz_profit AS biz_profit -- 营业利润
    ,H.net_profit AS net_profit -- 净利润
    ,H.total_assets_amt AS total_assets_amt -- 资产总额
    ,H.total_liab_amt AS total_liab_amt -- 负债总额
    ,H.owner_equity AS owner_equity -- 所有者权益
    ,H.biz_income_1 AS biz_income_1 -- 营业收入
    ,H.biz_profit_1 AS biz_profit_1 -- 营业利润
    ,H.net_profit_1 AS net_profit_1 -- 净利润
    ,H.eqty_appr_rate AS eqty_appr_rate -- 增资股权增值率
    ,H.muni_soe_ratio_chg AS muni_soe_ratio_chg -- 全部国有股东持股比例变动情况 （仅含市国资委监管国有企业股东）
    ,H.all_soe_ratio_chg AS all_soe_ratio_chg -- 全部国有股东持股比例变动情况 （含其他国资监管机构下属企业股东）
    ,H.strtg_invst_rsn AS strtg_invst_rsn -- 引入战略投资人/引入特殊资质股东/经同级国有资产监督管理机构批准
    ,H.is_new_shr_cap_inc AS is_new_shr_cap_inc -- 是否涉及新股东增资
    ,H.ent_level AS ent_level -- 企业层级
FROM std.std_bjhl_tcgq_zzzsgpxm_d A -- 增资挂牌项目 
INNER JOIN std.std_bjhl_tcgq_cjjl_d D -- 成交记录 
    ON D.prj = A.id
   AND D.dt = '${dmp_day}'
LEFT JOIN std.std_bjhl_tcgq_yxtzfxx_d B -- 意向投资方信息 
    ON D.prj = B.project_id
   AND D.investor = B.id 
   AND B.dt = '${dmp_day}'
LEFT JOIN std.std_bjhl_tbzzd_d B1 -- 标准字典 
    ON B1.cd = B.industry 
   AND B1.dict_tp = 2 
   AND LENGTH(B1.cd) > 1 
   AND B1.dt = '${dmp_day}'
LEFT JOIN
(
    SELECT  COUNT(1) AS qualified_investors_num
          , project_id
    FROM std.std_bjhl_tcgq_yxtzfxx_d
    WHERE sts = 11
    AND final_qlfy_rst = 1
    AND dt = '${dmp_day}'
    GROUP BY project_id
) C -- 意向受让方信息(征集合格意向投资人个数) 
    ON C.project_id = A.id
LEFT JOIN
(
    SELECT  e1.prj as project_id
          , SUM(e1.invest_total_w_yuan)  AS invest_total_w_yuan
          , SUM(e1.new_fndd_cptl_w_yuan) AS new_fndd_cptl_w_yuan
    FROM std.std_bjhl_tcgq_cjjl_d e1
    WHERE e1.dt = '${dmp_day}'
    GROUP BY e1.prj
) E -- 成交记录汇总 
    ON E.project_id = A.id
LEFT JOIN (
    SELECT 
        date_format(MAX(rq), 'yyyy-MM-dd') AS max_date
        , icap_prj_id 
    FROM std.std_bjhl_tcgq_zzzsgpxm_nd_d
    WHERE dt = '${dmp_day}'
    GROUP BY icap_prj_id
) F -- 增资挂牌项目年度 
    ON F.icap_prj_id = A.id 
-- 增资前股东 
LEFT JOIN
(
    SELECT  CAST(jg.tcgq_zzzsgpxm_id AS STRING)                AS xmid
          , CONCAT_WS('、',COLLECT_LIST(CONCAT(gdmc,REGEXP_REPLACE(cgbl || '%', '(\\.)?0+%$', '%')))) AS zzqgqjg
    FROM std.std_bjhl_tcgq_zzzsgpxm_t_gdczcg_d jg
    WHERE dt = '${dmp_day}'
    GROUP BY jg.tcgq_zzzsgpxm_id
) G
    ON G.xmid = A.id
-- 补录表 
LEFT JOIN (
    SELECT *
    FROM (
        SELECT *,
            ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
        FROM std.std_bl_icap_prj_data_d
        WHERE dt = '${dmp_day}'
    ) t 
    WHERE rn = 1
) H -- 企业增资项目数据纠正表-补录 
ON H.proj_no = A.project_code 
AND regexp_replace(trim(B.nm), '\\s+', '') = regexp_replace(trim(H.investor_name), '\\s+', '')
LEFT JOIN
(
	SELECT  CAST(project_id AS STRING) AS project_id
	       ,CONCAT_WS('、', COLLECT_LIST(shrh_nm || 
	            CASE 
	                WHEN final_ratio IS NOT NULL THEN 
	                    REGEXP_REPLACE(CAST(final_ratio AS STRING) || '%', '\.?0+%$', '%')
	                ELSE '0%'
	            END)) AS zzhgqjg
	FROM std.std_bjhl_tcgq_zzhgqjg_d  --增资后融资方股权结构
	WHERE dt = '${dmp_day}'
	GROUP BY project_id
) J
ON J.project_id = A.id
WHERE A.dt = '${dmp_day}'
AND A.deal_date IS NOT NULL -- 成交日期不为空
AND B.buyer_tp_dsc IN ('正式挂牌','非挂牌')

UNION ALL
-- 联合体成员
SELECT 
    A.project_code                   AS project_code -- 项目编号
   ,I.mbr_nm                         AS buyer_name     -- 投资方名称
   ,'是'                              AS is_joint_member -- 是否联合体成员
   ,CASE 
        WHEN A.project_code LIKE 'G6%' THEN '公开'
        WHEN A.project_code LIKE 'G7%' OR A.project_code LIKE 'G8%' THEN '非公开' 
    END                              AS transparency -- 公开/非公开 
   ,CASE 
            WHEN A.intnd_rs_cptl_crpnd_tp = 1 THEN 
                CASE 
                    WHEN A.intnd_rs_cptl_crpnd_mod = 2 THEN 
                        CASE WHEN A.intnd_rs_cptl_crpnd_hold_shr_pct = 0 THEN '0%'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_hold_shr_pct || '%', '(\\.)?0+%$', '%')
                        END
                    WHEN A.intnd_rs_cptl_crpnd_mod = 1 THEN 
                        CASE WHEN A.intnd_rs_cptl_crpnd_hold_shr_pct_min = 0 THEN '0%'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_hold_shr_pct_min || '%', '(\\.)?0+%$', '%')
                        END || '至' || 
                        CASE WHEN A.intnd_rs_cptl_crpnd_hold_shr_pct_max = 0 THEN '0%'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_hold_shr_pct_max || '%', '(\\.)?0+%$', '%')
                        END
                END
            WHEN A.intnd_rs_cptl_crpnd_tp = 2 THEN 
                CASE 
                    WHEN A.intnd_rs_cptl_crpnd_mod = 2 THEN 
                        CASE WHEN A.intnd_rs_cptl_crpnd_shr_num_w_share = 0 THEN '0万股'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_shr_num_w_share || '万股', '(\\.)?0+万股$', '万股')
                        END
                    WHEN A.intnd_rs_cptl_crpnd_mod = 1 THEN 
                        CASE WHEN A.intnd_rs_cptl_crpnd_shr_num_min_w_share = 0 THEN '0万股'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_shr_num_min_w_share || '万股', '(\\.)?0+万股$', '万股')
                        END || '至' || 
                        CASE WHEN A.intnd_rs_cptl_crpnd_shr_num_max_w_share = 0 THEN '0万股'
                        ELSE REGEXP_REPLACE(A.intnd_rs_cptl_crpnd_shr_num_max_w_share || '万股', '(\\.)?0+万股$', '万股')
                        END
                END 
    END				                 AS listing_cap_inc_ratio -- 挂牌增资比例/股数
   ,G.zzqgqjg                        AS pre_cap_inc_shareholders -- 增资前的股东 
   ,REGEXP_REPLACE(A.zzqgyzbczzbl || '%', '\.?0+%$', '%')      AS pre_municipal_state_owned_ratio -- 增资前全部国有股东持股比例（仅含市国资委监管国有企业股东） 
   ,REGEXP_REPLACE(A.zzqgyzbczzbl || '%', '\.?0+%$', '%')      AS pre_all_state_owned_ratio -- 增资前全部国有股东持股比例（含其他国资监管机构下属企业股东）  
   ,A.prefer_mod_dsc                 AS deal_method -- 成交方式 
   ,substr(A.evaluate_date,0,10)     AS assessment_baseline_date -- 评估基准日 
   ,A.asset_sum_book_val             AS total_assets -- 资产总额（万元） 
   ,A.liab_sum_book_val              AS total_liabilities -- 负债总额（万元） 
   ,A.zzhgyzbczzbl                   AS post_municipal_state_owned_ratio -- 项目完成后全部国有股东持股比例（%）（仅含市国资委监管国有企业股东） 
   ,A.zzhgyzbczzbl                   AS post_all_state_owned_ratio -- 项目完成后全部国有股东持股比例（%）（含其他国资监管机构下属企业股东） 
   ,A.icap_af_fincer_economy_type_dsc  AS post_project_nature -- 项目完成后标的企业性质 
   ,CASE 
        WHEN A.mdl_chk_opin_tp_dsc = '股权激励' THEN A.mdl_chk_opin_tp_dsc 
        ELSE '' 
    END                              AS esop -- 股权激励/员工持股 
   ,CASE 
        WHEN A.mdl_chk_opin_tp_dsc IN ('特定投资方增资','所出资企业直接或指定其控股、实际控制的其他子企业参与增资') THEN A.mdl_chk_opin_tp_dsc 
        ELSE '' 
    END                              AS reorg -- 集团内部重组/股权结构调整/所出资企业直接或指定其控股、实际控制的其他子企业参与增资/债转股 
   ,CASE 
        WHEN A.mdl_chk_opin_tp_dsc IN ('国有资本经营预算拨款','国有资本经营有预算拨款') THEN A.mdl_chk_opin_tp_dsc   
        ELSE '' 
    END                              AS budget -- 国有资本经营预算拨款 
   ,CASE 
        WHEN A.mdl_chk_opin_tp = 3   THEN '所有者权益类转增'
        ELSE '' 
    END                              AS equity_to_capital -- 所有者权益转增资本 
   ,CASE 
        WHEN A.mdl_chk_opin_tp_dsc IN ('原股东同比例','原股东不同比例') THEN A.mdl_chk_opin_tp_dsc 
        ELSE '' 
    END                              AS original_shareholder_increase -- 原股东增资（原股东同比例增资/原股东不同比例增资） 
   ,substr(A.approval_date,0,10)     AS approval_date -- 批准日期 
   ,C.qualified_investors_num        AS qualified_investors_num -- 征集合格意向投资人个数 
   ,CASE WHEN H.single_investor_amt IS NOT NULL THEN H.single_investor_amt ELSE D.repre_invest_total_w_yuan  END    AS single_investor_cap_inc_amt -- 单个投资人增资金额（万元） 
   ,D.repre_new_fndd_cptl_w_yuan     AS single_investor_reg_cap_amt -- 单个投资人增加注册资本金额（万元） 
   ,I.industry_dsc                   AS investor_industry -- 投资人所属行业 
   ,E.invest_total_w_yuan            AS total_new_capital -- 新增出资资本合计数 
   ,CASE WHEN H.financing_amt IS NOT NULL THEN H.financing_amt ELSE E.new_fndd_cptl_w_yuan   END    AS total_investment_funds -- 投资资金总额合计数 
   ,F.max_date                       AS audit_year -- 年度 
   ,H.mix_own_type                   AS mix_own_type -- 混改类型（补录） 
   ,H.land_use_amt                   AS land_use_amt -- 涉及土地使用权金额（补录） 
   ,H.tech_asset_amt                 AS tech_asset_amt -- 涉及技术资产金额（补录） 
   ,NULL                             AS bond_amt -- 涉及债权转让金额
   ,A.custd_org_depdc_prov           AS custd_org_depdc_prov   -- 监管机构属地(省)
   ,CASE
         WHEN A.intnd_rs_cptl_tot_amt_cmnt IS NOT NULL THEN
             substr(A.intnd_rs_cptl_tot_amt_cmnt, 3000, 1)
         ELSE
             CASE A.intnd_new_cptl_cptl_mod
                 WHEN 2 THEN
                     REGEXP_REPLACE(A.intnd_rs_cptl_tot_amt_w_yuan || '', '\.?0+$', '')
                 WHEN 1 THEN
                     REGEXP_REPLACE(A.intnd_rs_cptl_tot_amt_min_w_yuan || '', '\.?0+$', '') || '至' || 
                     REGEXP_REPLACE(A.intnd_rs_cptl_tot_amt_max_w_yuan || '', '\.?0+$', '')
             END
     END AS target_funds_amt               -- 拟募集资金金额（万元）
    ,A.equity_book_val_w_yuan AS equity -- 所有者权益（万元） 
    ,J.zzhgqjg AS post_project_shareholders      -- 项目完成后的股东 
    ,J.zzhgqjg AS post_project_shareholders_ratio -- 项目完成后的股东持股比例
    ,H.investor_type AS investor_type -- 投资人类型
    ,A.economy_type_dsc AS  economy_type_dsc -- 增资前标的企业性质
    ,H.pre_shr_ratio    AS  pre_shr_ratio -- 增资前的股东持股比例
    ,H.biz_income AS biz_income -- 营业收入
    ,H.biz_profit AS biz_profit -- 营业利润
    ,H.net_profit AS net_profit -- 净利润
    ,H.total_assets_amt AS total_assets_amt -- 资产总额
    ,H.total_liab_amt AS total_liab_amt -- 负债总额
    ,H.owner_equity AS owner_equity -- 所有者权益
    ,H.biz_income_1 AS biz_income_1 -- 营业收入
    ,H.biz_profit_1 AS biz_profit_1 -- 营业利润
    ,H.net_profit_1 AS net_profit_1 -- 净利润
    ,H.eqty_appr_rate AS eqty_appr_rate -- 增资股权增值率
    ,H.muni_soe_ratio_chg AS muni_soe_ratio_chg -- 全部国有股东持股比例变动情况 （仅含市国资委监管国有企业股东）
    ,H.all_soe_ratio_chg AS all_soe_ratio_chg -- 全部国有股东持股比例变动情况 （含其他国资监管机构下属企业股东）
    ,H.strtg_invst_rsn AS strtg_invst_rsn -- 引入战略投资人/引入特殊资质股东/经同级国有资产监督管理机构批准
    ,H.is_new_shr_cap_inc AS is_new_shr_cap_inc -- 是否涉及新股东增资
    ,H.ent_level AS ent_level -- 企业层级
FROM std.std_bjhl_tcgq_zzzsgpxm_d A -- 增资挂牌项目 
INNER JOIN std.std_bjhl_tcgq_cjjl_d D -- 成交记录 
    ON D.prj = A.id
   AND D.dt = '${dmp_day}'
LEFT JOIN std.std_bjhl_tcgq_yxtzfxx_d B -- 意向投资方信息 
    ON D.prj = B.project_id
   AND D.investor = B.id 
   AND B.dt = '${dmp_day}'
LEFT JOIN std.std_bjhl_tbzzd_d B1 -- 标准字典 
    ON B1.cd = B.industry 
   AND B1.dict_tp = 2 
   AND LENGTH(B1.cd) > 1 
   AND B1.dt = '${dmp_day}'
LEFT JOIN
(
    SELECT  COUNT(1) AS qualified_investors_num
          , project_id
    FROM std.std_bjhl_tcgq_yxtzfxx_d
    WHERE sts = 11
    AND final_qlfy_rst = 1
    AND dt = '${dmp_day}'
    GROUP BY project_id
) C -- 意向受让方信息(征集合格意向投资人个数) 
    ON C.project_id = A.id
LEFT JOIN
(
    SELECT  e1.prj as project_id
          , SUM(e1.invest_total_w_yuan)  AS invest_total_w_yuan
          , SUM(e1.new_fndd_cptl_w_yuan) AS new_fndd_cptl_w_yuan
    FROM std.std_bjhl_tcgq_cjjl_d e1
    WHERE e1.dt = '${dmp_day}'
    GROUP BY e1.prj
) E -- 成交记录汇总 
    ON E.project_id = A.id
LEFT JOIN (
    SELECT 
        date_format(MAX(rq), 'yyyy-MM-dd') AS max_date
        , icap_prj_id 
    FROM std.std_bjhl_tcgq_zzzsgpxm_nd_d
    WHERE dt = '${dmp_day}'
    GROUP BY icap_prj_id
) F -- 增资挂牌项目年度 
    ON F.icap_prj_id = A.id 
-- 增资前股东 
LEFT JOIN
(
    SELECT  CAST(jg.tcgq_zzzsgpxm_id AS STRING)                AS xmid
          , CONCAT_WS('、',COLLECT_LIST(CONCAT(gdmc,REGEXP_REPLACE(cgbl || '%', '(\\.)?0+%$', '%')))) AS zzqgqjg
    FROM std.std_bjhl_tcgq_zzzsgpxm_t_gdczcg_d jg
    WHERE dt = '${dmp_day}'
    GROUP BY jg.tcgq_zzzsgpxm_id
) G
    ON G.xmid = A.id
-- 补录表 
LEFT JOIN (
    SELECT *
    FROM (
        SELECT *,
            ROW_NUMBER() OVER(PARTITION BY proj_no, investor_name ORDER BY udt_tm DESC) as rn
        FROM std.std_bl_icap_prj_data_d
        WHERE dt = '${dmp_day}'
    ) t 
    WHERE rn = 1
) H -- 企业增资项目数据纠正表-补录 
ON H.proj_no = A.project_code 
AND regexp_replace(trim(B.nm), '\\s+', '') = regexp_replace(trim(H.investor_name), '\\s+', '')
-- 联合体成员信息
LEFT JOIN std.std_bjhl_tcgq_yxtzf_lhtcy_d I
    ON I.intnt_investor_id = B.id
   AND I.dt = '${dmp_day}'
LEFT JOIN
(
	SELECT  CAST(project_id AS STRING) AS project_id
	       ,CONCAT_WS('、', COLLECT_LIST(shrh_nm || 
	            CASE 
	                WHEN final_ratio IS NOT NULL THEN 
	                    REGEXP_REPLACE(CAST(final_ratio AS STRING) || '%', '\.?0+%$', '%')
	                ELSE '0%'
	            END)) AS zzhgqjg
	FROM std.std_bjhl_tcgq_zzhgqjg_d  --增资后融资方股权结构
	WHERE dt = '${dmp_day}'
	GROUP BY project_id
) J
ON J.project_id = A.id
WHERE A.dt = '${dmp_day}'
AND A.deal_date IS NOT NULL -- 成交日期不为空
AND B.buyer_tp_dsc IN ('正式挂牌','非挂牌')
AND B.is_unite_ivs = 1
